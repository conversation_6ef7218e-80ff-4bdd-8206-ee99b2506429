- skip OS customisation settings before flashing
- ********
- login with pi raspberry

sudo nmcli con mod usb0 ipv4.dns ""
sudo systemctl restart NetworkManager

sudo passwd
sudo nano /etc/ssh/sshd_config
- edit line: PermitRootLogin yes
sudo service sshd restart

sudo raspi-config
- System Options / Password
- Localisation / Timezone / Asia / Kolkata
- Localisation / WLAN Country / IN

sudo bluetoothctl
scan on
pair 88:B9:45:6F:BC:16
trust 88:B9:45:6F:BC:16
scan off
exit

sudo apt update
# NEVER USE THIS: sudo apt upgrade
sudo apt-get install btop aircrack-ng nmap macchanger btop neofetch -y

- requires pi user for pi apps
# wget -qO- https://raw.githubusercontent.com/Botspot/pi-apps/master/install | bash
# ls ~/pi-apps/apps
# ~/pi-apps/manage install 'All Is Well'
# ~/pi-apps/manage install Fastfetch
# ~/pi-apps/manage install 'More RAM'
# ~/pi-apps/updater
# ~/All-is-well/aiw/aiwrpi.sh 
sudo reboot now

- login with root 3693

sudo pwnagotchi --wizard
- N,Y,dank-pwn,0,N,Y,waveshare_4,N
sudo pwnagotchi plugins update
sudo pwnagotchi plugins list
source ~pi/.pwn/bin/activate
pip3 install -U requests dpkt qrcode python-telegram-bot psutil
- add all files
sudo reboot now
