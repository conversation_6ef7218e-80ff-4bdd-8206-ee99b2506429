main.name = "dank-pwn"
main.plugins.bt-tether.enabled = true
main.plugins.bt-tether.phone-name = "dank.iphone-13-pro"
main.plugins.bt-tether.phone = "ios"
main.plugins.bt-tether.ip = "***********"
main.plugins.bt-tether.mac = "88:B9:45:6F:BC:16"

main.plugins.auto-tune.enabled = true
main.plugins.auto-tune.show_hidden = false
main.plugins.auto-tune.reset_history = true
main.plugins.auto-tune.extra_channels = 15

main.plugins.auto-update.enabled = true
main.plugins.auto-update.install = true
main.plugins.auto-update.interval = 1

main.plugins.fix_services.enabled = true

main.plugins.gdrivesync.enabled = false
main.plugins.gdrivesync.backupfiles = [ "",]
main.plugins.gdrivesync.backup_folder = "PwnagotchiBackups"

main.plugins.gpio_buttons.enabled = false

main.plugins.gps.enabled = false
main.plugins.gps.speed = 19200
main.plugins.gps.device = "/dev/ttyUSB0"

main.plugins.gps_listener.enabled = false

main.plugins.grid.enabled = true
main.plugins.grid.report = true

main.plugins.logtail.enabled = true
main.plugins.logtail.max-lines = 10000

main.plugins.memtemp.enabled = false
main.plugins.memtemp.scale = "celsius"
main.plugins.memtemp.orientation = "horizontal"

main.plugins.ohcapi.enabled = false
main.plugins.ohcapi.api_key = "sk_your_api_key_here"
main.plugins.ohcapi.receive_email = "yes"

main.plugins.pwndroid.enabled = false
main.plugins.pwndroid.display = false
main.plugins.pwndroid.display_altitude = false

main.plugins.pisugarx.enabled = false
main.plugins.pisugarx.rotation = false
main.plugins.pisugarx.default_display = "percentage"

main.plugins.session-stats.enabled = true
main.plugins.session-stats.save_directory = "/var/tmp/pwnagotchi/sessions/"

main.plugins.ups_hat_c.enabled = false
main.plugins.ups_hat_c.label_on = true
main.plugins.ups_hat_c.shutdown = 5
main.plugins.ups_hat_c.bat_x_coord = 140
main.plugins.ups_hat_c.bat_y_coord = 0

main.plugins.ups_lite.enabled = false
main.plugins.ups_lite.shutdown = 2

main.plugins.webcfg.enabled = true

main.plugins.webgpsmap.enabled = false

main.plugins.wigle.enabled = false
main.plugins.wigle.api_key = ""
main.plugins.wigle.donate = false

main.plugins.wpa-sec.enabled = true
main.plugins.wpa-sec.api_key = "8ed2cd8259566fa61efef5a2befbe1d0"
main.plugins.wpa-sec.api_url = "https://wpa-sec.stanev.org"
main.plugins.wpa-sec.download_results = false
main.plugins.wpa-sec.show_pwd = false

main.plugins.display-password.enabled = true

main.plugins.enable_assoc.enabled = true

main.plugins.enable_deauth.enabled = true

main.plugins.exp.enabled = false

main.plugins.IPDisplay.enabled = true
main.plugins.IPDisplay.skip_devices = [ "lo",]

main.plugins.handshakes-dl.enabled = true

main.plugins.internet-connection.enabled = false

main.plugins.memtemp-plus.enabled = true
main.plugins.memtemp-plus.scale = "celsius"
main.plugins.memtemp-plus.orientation = "horizontal"

main.plugins.tweak_view.enabled = true

main.plugins.wpa-sec-list.enabled = false

main.plugins.instattack.enabled = true

main.plugins.dashboard2.enabled = false

main.plugins.expv2.enabled = false

main.plugins.pwncrack.enabled = true
main.plugins.pwncrack.key = "233fd508-6472-4ba5-ae63-09071609a702"

main.plugins.sorted_pwn.enabled = true

main.plugins.onlinehashcrack.enabled = true

main.plugins.better_quickdic.enabled = true
main.plugins.better_quickdic.face = "(·ω·)"
main.plugins.better_quickdic.wordlist_folder = "/home/<USER>/wordlists/"
main.plugins.better_quickdic.api = ""
main.plugins.better_quickdic.id = ""

main.plugins.internet-conection.enabled = false

main.plugins.clock.enabled = true

main.plugins.webssh.enabled = true

main.plugins.probeReq.enabled = true

main.plugins.educational-purposes-only.enabled = false
main.plugins.educational-purposes-only.home-network = "Kabir 2.4"
main.plugins.educational-purposes-only.home-password = "Kabir1977@"
main.plugins.educational-purposes-only.minimum-signal-strength = -75

main.plugins.aircrackonly.enabled = true
main.plugins.aircrackonly.face = "(>.<)"

main.plugins.memtempV2.enabled = false

main.lang = "en"
main.whitelist = [
 "Kabir 5",
 "Kabir 2.4",
 "Ray 5GHz",
 "Ray 2.4GHz",
 "dank.iphone-13-pro",
]
main.confd = "/etc/pwnagotchi/conf.d/"
main.custom_plugin_repos = [
 "https://github.com/jayofelony/pwnagotchi-torch-plugins/archive/master.zip",
 "https://github.com/Sniffleupagus/pwnagotchi_plugins/archive/master.zip",
 "https://github.com/NeonLightning/pwny/archive/master.zip",
 "https://github.com/marbasec/UPSLite_Plugin_1_3/archive/master.zip",
 "https://github.com/wpa-2/Pwnagotchi-Plugins/archive/master.zip",
]
main.custom_plugins = "/usr/local/share/pwnagotchi/custom-plugins/"
main.plugin.gdrivesync.interval = 1

main.iface = "wlan0mon"
main.mon_start_cmd = "/usr/bin/monstart"
main.mon_stop_cmd = "/usr/bin/monstop"
main.mon_max_blind_epochs = 5
main.no_restart = false
main.log.path = "/etc/pwnagotchi/log/pwnagotchi.log"
main.log.path-debug = "/etc/pwnagotchi/log/pwnagotchi-debug.log"
main.log.rotation.enabled = true
main.log.rotation.size = "10M"

ui.display.enabled = true
ui.display.type = "waveshare_4"
ui.display.rotation = 0

ui.invert = false
ui.cursor = true
ui.fps = 0
ui.font.name = "DejaVuSansMono"
ui.font.size_offset = 0

ui.faces.look_r = "( ⚆_⚆)"
ui.faces.look_l = "(☉_☉ )"
ui.faces.look_r_happy = "( ◕‿◕)"
ui.faces.look_l_happy = "(◕‿◕ )"
ui.faces.sleep = "(⇀‿‿↼)"
ui.faces.sleep2 = "(≖‿‿≖)"
ui.faces.awake = "(◕‿‿◕)"
ui.faces.bored = "(-__-)"
ui.faces.intense = "(°▃▃°)"
ui.faces.cool = "(⌐■_■)"
ui.faces.happy = "(•‿‿•)"
ui.faces.excited = "(ᵔ◡◡ᵔ)"
ui.faces.grateful = "(^‿‿^)"
ui.faces.motivated = "(☼‿‿☼)"
ui.faces.demotivated = "(≖__≖)"
ui.faces.smart = "(✜‿‿✜)"
ui.faces.lonely = "(ب__ب)"
ui.faces.sad = "(╥☁╥ )"
ui.faces.angry = "(-_-')"
ui.faces.friend = "(♥‿‿♥)"
ui.faces.broken = "(☓‿‿☓)"
ui.faces.debug = "(#__#)"
ui.faces.upload = "(1__0)"
ui.faces.upload1 = "(1__1)"
ui.faces.upload2 = "(0__1)"
ui.faces.png = false
ui.faces.position_x = 0
ui.faces.position_y = 34

ui.web.enabled = true
ui.web.address = "::"
ui.web.auth = false
ui.web.username = "pwnagotchi"
ui.web.password = "pwnagotchi"
ui.web.origin = ""
ui.web.port = 8080
ui.web.on_frame = ""

personality.advertise = true
personality.deauth = true
personality.associate = true
personality.channels = [
 1,
 2,
 3,
 11,
 6,
 5,
 1,
 2,
 8,
 9,
 12,
 10,
 3,
 7,
 13,
 4,
]
personality.min_rssi = -200
personality.ap_ttl = 120
personality.sta_ttl = 300
personality.recon_time = 30
personality.max_inactive_scale = 2
personality.recon_inactive_multiplier = 2
personality.hop_recon_time = 10
personality.min_recon_time = 5
personality.max_interactions = 3
personality.max_misses_for_recon = 5
personality.excited_num_epochs = 10
personality.bored_num_epochs = 15
personality.sad_num_epochs = 25
personality.bond_encounters_factor = 20000
personality.throttle_a = 0.4
personality.throttle_d = 0.9

bettercap.handshakes = "/home/<USER>/handshakes"
bettercap.silence = [
 "ble.device.new",
 "ble.device.lost",
 "ble.device.disconnected",
 "ble.device.connected",
 "ble.device.service.discovered",
 "ble.device.characteristic.discovered",
 "wifi.client.new",
 "wifi.client.lost",
 "wifi.client.probe",
 "wifi.ap.new",
 "wifi.ap.lost",
 "mod.started",
]

fs.memory.enabled = true
fs.memory.mounts.log.enabled = true
fs.memory.mounts.log.mount = "/etc/pwnagotchi/log/"
fs.memory.mounts.log.size = "50M"
fs.memory.mounts.log.sync = 60
fs.memory.mounts.log.zram = true
fs.memory.mounts.log.rsync = true

fs.memory.mounts.data.enabled = true
fs.memory.mounts.data.mount = "/var/tmp/pwnagotchi"
fs.memory.mounts.data.size = "10M"
fs.memory.mounts.data.sync = 3600
fs.memory.mounts.data.zram = true
fs.memory.mounts.data.rsync = true

