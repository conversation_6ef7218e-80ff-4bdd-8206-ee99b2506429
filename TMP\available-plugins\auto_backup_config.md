```
[main.plugins.auto_backup]
enabled = false
interval = "daily"    # or "hourly", or a number (minutes)
max_tries = 0
backup_location = "/home/<USER>/"
files = [
  "/root/settings.yaml",
  "/root/client_secrets.json",
  "/root/.api-report.json",
  "/root/.ssh",
  "/root/.bashrc",
  "/root/.profile",
  "/home/<USER>/handshakes",
  "/root/peers",
  "/etc/pwnagotchi/",
  "/usr/local/share/pwnagotchi/custom-plugins",
  "/etc/ssh/",
  "/home/<USER>/.bashrc",
  "/home/<USER>/.profile",
  "/home/<USER>/.wpa_sec_uploads"
]
exclude = [ "/etc/pwnagotchi/logs/*"]
commands = [ "tar cf {backup_file} {files}"]

```


To restore after flashing 
```
sudo tar xf /home/<USER>/NAME-backup.tar -C /
```


