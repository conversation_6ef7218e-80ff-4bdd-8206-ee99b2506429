
# Dank UI Plugin - Enhanced UI with IP Display and Probe Request Monitoring
#
# Combines features from IPDisplay.py and probeReq.py with enhanced UI management
# Provides comprehensive network monitoring and system information display
#
# Features:
# - Network interface IP address cycling display
# - WiFi probe request monitoring and display
# - System metrics display (memory, CPU, temperature, etc.)
# - Enhanced error handling and logging
# - Configurable positioning and refresh rates
# - Support for all display types
# - Multiple display modes and layouts
# - Real-time network activity monitoring
#
# Author: SirDank
# Based on work by: NeonLightning, NurseJackass, jayofelony, avipars, unitMeasure
# Version: 3.0.0
# License: GPL3

import logging
import os
import time
import subprocess
import ipaddress
import pwnagotchi
import pwnagotchi.plugins as plugins
import pwnagotchi.ui.fonts as fonts
from pwnagotchi.ui.components import LabeledValue, Text
from pwnagotchi.ui.view import BLACK


class DankUI(plugins.Plugin):
    __author__ = 'SirDank'
    __version__ = '3.0.0'
    __license__ = 'GPL3'
    __description__ = 'Enhanced UI with IP display, probe request monitoring, and system metrics'

    def __init__(self):
        self.options = dict()
        self.ready = False
        self.running = True

        # IP Display functionality
        self.device_skip_list = ['lo']
        self.device_index = 0
        self.last_ip_update_time = 0
        self.ip_delay_time = 2

        # Probe Request functionality
        self.pr_status = "Waiting..."
        self.last_probe_time = 0
        self.probe_timeout = 10  # seconds to show probe info

        # System metrics cache
        self._cache = {}
        self._last_update = 0

        # UI elements tracking
        self.ui_elements = []

    def on_loaded(self):
        """Initialize plugin when loaded"""
        try:
            # Set default options if not provided
            self._set_default_options()

            # Configure IP display settings
            if 'ip_delay_time' in self.options:
                self.ip_delay_time = float(self.options['ip_delay_time'])
            if 'skip_devices' in self.options:
                self.device_skip_list = self.options['skip_devices']
            if 'probe_timeout' in self.options:
                self.probe_timeout = float(self.options['probe_timeout'])

            # Ensure lo is always in skip list
            if 'lo' not in self.device_skip_list:
                self.device_skip_list.append('lo')

            logging.info(f"[DankUI] Plugin v{self.__version__} loaded successfully")
            logging.debug(f"[DankUI] Skip devices: {self.device_skip_list}")
            logging.debug(f"[DankUI] IP delay: {self.ip_delay_time}s, Probe timeout: {self.probe_timeout}s")

        except Exception as e:
            logging.error(f"[DankUI] Error during plugin loading: {e}")
            logging.debug(f"[DankUI] Loading error details:", exc_info=True)

    def on_ready(self, agent):
        """Called when agent is ready"""
        try:
            self._agent = agent
            self.ready = True
            logging.info("[DankUI] Plugin ready")
        except Exception as e:
            logging.error(f"[DankUI] Error in on_ready: {e}")

    def _set_default_options(self):
        """Set default options if not provided in config"""
        defaults = {
            'enabled': 'true',
            'ip_delay_time': '2',
            'probe_timeout': '10',
            'skip_devices': ['lo', 'eth0'],
            'show_system_info': 'true',
            'show_ip_display': 'true',
            'show_probe_requests': 'true',
            'refresh_interval': '5',
            'ip_position': '0,95',
            'probe_position': '0,105',
            'system_position': '150,95',
        }

        for key, default_value in defaults.items():
            if key not in self.options:
                self.options[key] = default_value

    def get_interface_addresses(self):
        """Get network interface addresses using ip command"""
        try:
            command = "ip -4 -o addr | awk '/inet / {print $2 \":\" $4}' | cut -d '/' -f 1"
            interfaces = []

            output = subprocess.getoutput(command)
            for line in output.split('\n'):
                line = line.strip()
                if line and ':' in line:
                    interface, ip = line.split(':', 1)
                    if interface.lower() not in self.device_skip_list:
                        interfaces.append(f"{interface}:{ip}")

            return interfaces
        except Exception as e:
            logging.error(f"[DankUI] Error getting interface addresses: {e}")
            return []

    def get_current_ip_display(self):
        """Get current IP address to display"""
        try:
            interfaces = self.get_interface_addresses()
            if not interfaces:
                return "No interfaces"

            # Cycle through interfaces
            if self.device_index >= len(interfaces):
                self.device_index = 0

            current_interface = interfaces[self.device_index]

            # Special handling for Bluetooth interfaces
            if 'bnep0' in current_interface:
                try:
                    # Check if Bluetooth devices are connected
                    bt_output = subprocess.check_output(['hcitool', 'con'], stderr=subprocess.DEVNULL)
                    if len(bt_output.strip()) == 0:
                        # No BT connections, skip to next interface
                        self.device_index += 1
                        if self.device_index >= len(interfaces):
                            self.device_index = 0
                        if interfaces:
                            current_interface = interfaces[self.device_index]
                except (subprocess.CalledProcessError, FileNotFoundError):
                    # hcitool not available or error, continue with current interface
                    pass

            return current_interface

        except Exception as e:
            logging.error(f"[DankUI] Error getting current IP display: {e}")
            return "Error"

    def get_system_info(self):
        """Get basic system information"""
        try:
            if 'system_info' in self._cache and not self._should_update_cache():
                return self._cache['system_info']

            # Get memory usage
            mem_usage = int(pwnagotchi.mem_usage() * 100)

            # Get CPU load
            cpu_load = int(pwnagotchi.cpu_load() * 100)

            # Get temperature
            temp = int(pwnagotchi.temperature())

            result = f"M:{mem_usage}% C:{cpu_load}% T:{temp}°C"
            self._cache['system_info'] = result
            return result

        except Exception as e:
            logging.error(f"[DankUI] Error getting system info: {e}")
            return "System: N/A"

    def _should_update_cache(self):
        """Check if cache should be updated based on refresh interval"""
        current_time = time.time()
        refresh_interval = float(self.options.get('refresh_interval', 5))
        return (current_time - self._last_update) >= refresh_interval

    def _update_cache_timestamp(self):
        """Update the last cache update timestamp"""
        self._last_update = time.time()

    def _parse_position(self, position_str, default_pos):
        """Parse position string into tuple"""
        try:
            if position_str and ',' in position_str:
                x, y = position_str.split(',', 1)
                return (int(x.strip()), int(y.strip()))
        except Exception as e:
            logging.debug(f"[DankUI] Position parsing failed: {e}")
        return default_pos

    def on_ui_setup(self, ui):
        """Setup UI elements"""
        try:
            # Parse positions
            ip_pos = self._parse_position(self.options.get('ip_position', '0,95'), (0, 95))
            probe_pos = self._parse_position(self.options.get('probe_position', '0,105'), (0, 105))
            system_pos = self._parse_position(self.options.get('system_position', '150,95'), (150, 95))

            # Setup IP display if enabled
            if self.options.get('show_ip_display', 'true').lower() == 'true':
                ui.add_element(
                    'dank_ip_display',
                    LabeledValue(
                        color=BLACK,
                        label="",
                        value='Initializing...',
                        position=ip_pos,
                        label_font=fonts.Small,
                        text_font=fonts.Small
                    )
                )
                self.ui_elements.append('dank_ip_display')

            # Setup probe request display if enabled
            if self.options.get('show_probe_requests', 'true').lower() == 'true':
                ui.add_element(
                    'dank_probe_status',
                    LabeledValue(
                        color=BLACK,
                        label="",
                        value='Probe: Waiting...',
                        position=probe_pos,
                        label_font=fonts.Small,
                        text_font=fonts.Small
                    )
                )
                self.ui_elements.append('dank_probe_status')

            # Setup system info display if enabled
            if self.options.get('show_system_info', 'true').lower() == 'true':
                ui.add_element(
                    'dank_system_info',
                    LabeledValue(
                        color=BLACK,
                        label="",
                        value='System: Loading...',
                        position=system_pos,
                        label_font=fonts.Small,
                        text_font=fonts.Small
                    )
                )
                self.ui_elements.append('dank_system_info')

            logging.info(f"[DankUI] UI setup completed with {len(self.ui_elements)} elements")

        except Exception as e:
            logging.error(f"[DankUI] Error during UI setup: {e}")
            logging.debug(f"[DankUI] UI setup error details:", exc_info=True)

    def on_ui_update(self, ui):
        """Update UI elements"""
        try:
            current_time = time.time()

            # Update IP display
            if 'dank_ip_display' in self.ui_elements:
                if current_time - self.last_ip_update_time >= self.ip_delay_time:
                    self.last_ip_update_time = current_time
                    self.device_index += 1
                    ip_display = self.get_current_ip_display()
                    ui.set('dank_ip_display', ip_display)

            # Update system info
            if 'dank_system_info' in self.ui_elements:
                if self._should_update_cache():
                    system_info = self.get_system_info()
                    ui.set('dank_system_info', system_info)
                    self._update_cache_timestamp()

            # Update probe status (check if probe info should timeout)
            if 'dank_probe_status' in self.ui_elements:
                if current_time - self.last_probe_time > self.probe_timeout:
                    ui.set('dank_probe_status', 'Probe: Waiting...')
                else:
                    ui.set('dank_probe_status', self.pr_status)

        except Exception as e:
            logging.error(f"[DankUI] Error during UI update: {e}")

    def on_bcap_wifi_client_probe(self, agent, event):
        """Handle WiFi client probe requests"""
        try:
            if not self.running:
                return

            probe = event['data']
            essid = probe.get('essid', 'Unknown')

            # Update probe status
            self.pr_status = f"Probe: {essid}"
            self.last_probe_time = time.time()

            logging.info(f"[DankUI] WiFi Probe Request: {probe}")

        except Exception as e:
            logging.error(f"[DankUI] Error handling probe request: {e}")

    def on_unload(self, ui):
        """Clean up UI elements when plugin is unloaded"""
        try:
            self.running = False

            with ui._lock:
                for element_name in self.ui_elements:
                    try:
                        ui.remove_element(element_name)
                        logging.debug(f"[DankUI] Removed UI element: {element_name}")
                    except Exception as e:
                        logging.debug(f"[DankUI] Could not remove element {element_name}: {e}")

            self.ui_elements.clear()
            logging.info("[DankUI] Plugin unloaded successfully")

        except Exception as e:
            logging.error(f"[DankUI] Error during plugin unload: {e}")


# Configuration Example:
# Add this to your /etc/pwnagotchi/config.toml file:
#
# [main.plugins.dank-ui]
# enabled = true
#
# # IP Display Settings
# show_ip_display = true
# ip_delay_time = 2
# skip_devices = ["lo", "eth0", "usb0"]
# ip_position = "0,95"
#
# # Probe Request Settings
# show_probe_requests = true
# probe_timeout = 10
# probe_position = "0,105"
#
# # System Info Settings
# show_system_info = true
# refresh_interval = 5
# system_position = "150,95"
#
# Feature Descriptions:
# - IP Display: Cycles through network interfaces showing IP addresses
# - Probe Requests: Monitors and displays WiFi probe requests from nearby devices
# - System Info: Shows memory usage, CPU load, and temperature
# - Configurable positioning for all display elements
# - Skip unwanted network interfaces from display
# - Adjustable refresh rates and timeouts
#
# Position Format: "x,y" coordinates on the display
# Skip Devices: List of interface names to exclude from IP cycling
# Timeouts: Time in seconds for various refresh intervals
